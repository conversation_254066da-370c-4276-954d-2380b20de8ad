#!/bin/bash

# Populate database with dummy data

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to check API response
check_response() {
  local response=$1
  local operation=$2

  if [[ $response == *"error"* ]] || [[ -z "$response" ]]; then
    echo -e "${RED}Error: Failed to $operation. Response: $response${NC}"
    return 1
  fi
  return 0
}

echo -e "${YELLOW}This script will populate the database with dummy data.${NC}"
echo -e "${YELLOW}Make sure your backend is already running.${NC}"

# Create users
echo -e "${YELLOW}Creating users...${NC}"

# User 1 - Will have multiple feeds with multiple posts
USER1_RESPONSE=$(curl -s -X POST http://localhost:8000/users/ \
  -H "Content-Type: application/json" \
  -d '{
    "username": "alice",
    "email": "<EMAIL>",
    "password": "password123"
  }')

# Print the full response for debugging
echo "User 1 response: $USER1_RESPONSE"

if ! check_response "$USER1_RESPONSE" "create user alice"; then
  echo -e "${YELLOW}Continuing with other operations...${NC}"
  USER1_ID="unknown"
else
  USER1_ID=$(echo $USER1_RESPONSE | grep -o '"id":"[^"]*' | cut -d'"' -f4)
  if [[ -z "$USER1_ID" ]]; then
    echo -e "${RED}Error: Could not extract user ID for alice${NC}"
    USER1_ID="unknown"
  else
    echo -e "${GREEN}Created user: alice (ID: $USER1_ID)${NC}"
  fi
fi

# User 2 - Will have one feed with no posts
USER2_RESPONSE=$(curl -s -X POST http://localhost:8000/users/ \
  -H "Content-Type: application/json" \
  -d '{
    "username": "bob",
    "email": "<EMAIL>",
    "password": "password123"
  }')

# Print the full response for debugging
echo "User 2 response: $USER2_RESPONSE"

if ! check_response "$USER2_RESPONSE" "create user bob"; then
  echo -e "${YELLOW}Continuing with other operations...${NC}"
  USER2_ID="unknown"
else
  USER2_ID=$(echo $USER2_RESPONSE | grep -o '"id":"[^"]*' | cut -d'"' -f4)
  if [[ -z "$USER2_ID" ]]; then
    echo -e "${RED}Error: Could not extract user ID for bob${NC}"
    USER2_ID="unknown"
  else
    echo -e "${GREEN}Created user: bob (ID: $USER2_ID)${NC}"
  fi
fi

# User 3 - Will have no feeds
USER3_RESPONSE=$(curl -s -X POST http://localhost:8000/users/ \
  -H "Content-Type: application/json" \
  -d '{
    "username": "charlie",
    "email": "<EMAIL>",
    "password": "password123"
  }')

# Print the full response for debugging
echo "User 3 response: $USER3_RESPONSE"

if ! check_response "$USER3_RESPONSE" "create user charlie"; then
  echo -e "${YELLOW}Continuing with other operations...${NC}"
  USER3_ID="unknown"
else
  USER3_ID=$(echo $USER3_RESPONSE | grep -o '"id":"[^"]*' | cut -d'"' -f4)
  if [[ -z "$USER3_ID" ]]; then
    echo -e "${RED}Error: Could not extract user ID for charlie${NC}"
    USER3_ID="unknown"
  else
    echo -e "${GREEN}Created user: charlie (ID: $USER3_ID)${NC}"
  fi
fi

# Create feeds for User 1
echo -e "${YELLOW}Creating feeds for alice...${NC}"

# Skip feed creation if user ID is unknown
if [[ "$USER1_ID" == "unknown" ]]; then
  echo -e "${RED}Skipping feed creation for alice due to missing user ID${NC}"
else

# Feed 1 - Will have multiple posts
FEED1_RESPONSE=$(curl -s -X POST http://localhost:8000/feed/ \
  -H "Content-Type: application/json" \
  -d "{
    \"name\": \"Alice's Tech Blog\",
    \"user_id\": \"$USER1_ID\"
  }")

if ! check_response "$FEED1_RESPONSE" "create feed Alice's Tech Blog"; then
  echo -e "${YELLOW}Continuing with other operations...${NC}"
  FEED1_ID="unknown"
else
  FEED1_ID=$(echo $FEED1_RESPONSE | grep -o '"id":"[^"]*' | cut -d'"' -f4)
  if [[ -z "$FEED1_ID" ]]; then
    echo -e "${RED}Error: Could not extract feed ID for Alice's Tech Blog${NC}"
    FEED1_ID="unknown"
  else
    echo -e "${GREEN}Created feed: Alice's Tech Blog (ID: $FEED1_ID)${NC}"
  fi
fi

# Feed 2 - Will have one post
FEED2_RESPONSE=$(curl -s -X POST http://localhost:8000/feed/ \
  -H "Content-Type: application/json" \
  -d "{
    \"name\": \"Alice's Travel Journal\",
    \"user_id\": \"$USER1_ID\"
  }")

if ! check_response "$FEED2_RESPONSE" "create feed Alice's Travel Journal"; then
  echo -e "${YELLOW}Continuing with other operations...${NC}"
  FEED2_ID="unknown"
else
  FEED2_ID=$(echo $FEED2_RESPONSE | grep -o '"id":"[^"]*' | cut -d'"' -f4)
  if [[ -z "$FEED2_ID" ]]; then
    echo -e "${RED}Error: Could not extract feed ID for Alice's Travel Journal${NC}"
    FEED2_ID="unknown"
  else
    echo -e "${GREEN}Created feed: Alice's Travel Journal (ID: $FEED2_ID)${NC}"
  fi
fi

# Feed 3 - Will have no posts
FEED3_RESPONSE=$(curl -s -X POST http://localhost:8000/feed/ \
  -H "Content-Type: application/json" \
  -d "{
    \"name\": \"Alice's Empty Feed\",
    \"user_id\": \"$USER1_ID\"
  }")

if ! check_response "$FEED3_RESPONSE" "create feed Alice's Empty Feed"; then
  echo -e "${YELLOW}Continuing with other operations...${NC}"
  FEED3_ID="unknown"
else
  FEED3_ID=$(echo $FEED3_RESPONSE | grep -o '"id":"[^"]*' | cut -d'"' -f4)
  if [[ -z "$FEED3_ID" ]]; then
    echo -e "${RED}Error: Could not extract feed ID for Alice's Empty Feed${NC}"
    FEED3_ID="unknown"
  else
    echo -e "${GREEN}Created feed: Alice's Empty Feed (ID: $FEED3_ID)${NC}"
  fi
fi

fi # End of alice's feeds

# Create feed for User 2
echo -e "${YELLOW}Creating feed for bob...${NC}"

# Skip feed creation if user ID is unknown
if [[ "$USER2_ID" == "unknown" ]]; then
  echo -e "${RED}Skipping feed creation for bob due to missing user ID${NC}"
else
  FEED4_RESPONSE=$(curl -s -X POST http://localhost:8000/feed/ \
    -H "Content-Type: application/json" \
    -d "{
      \"name\": \"Bob's Empty Feed\",
      \"user_id\": \"$USER2_ID\"
    }")

  if ! check_response "$FEED4_RESPONSE" "create feed Bob's Empty Feed"; then
    echo -e "${YELLOW}Continuing with other operations...${NC}"
    FEED4_ID="unknown"
  else
    FEED4_ID=$(echo $FEED4_RESPONSE | grep -o '"id":"[^"]*' | cut -d'"' -f4)
    if [[ -z "$FEED4_ID" ]]; then
      echo -e "${RED}Error: Could not extract feed ID for Bob's Empty Feed${NC}"
      FEED4_ID="unknown"
    else
      echo -e "${GREEN}Created feed: Bob's Empty Feed (ID: $FEED4_ID)${NC}"
    fi
  fi
fi

# Create posts for Feed 1
echo -e "${YELLOW}Creating posts for Alice's Tech Blog...${NC}"

# Skip post creation if feed ID is unknown
if [[ "$FEED1_ID" == "unknown" ]] || [[ "$USER1_ID" == "unknown" ]]; then
  echo -e "${RED}Skipping post creation for Alice's Tech Blog due to missing IDs${NC}"
else
  for i in {1..5}; do
    POST_RESPONSE=$(curl -s -X POST http://localhost:8000/feed/$FEED1_ID/post \
      -H "Content-Type: application/json" \
      -d "{
        \"content\": \"Tech post $i: Lorem ipsum dolor sit amet, consectetur adipiscing elit.\",
        \"user_id\": \"$USER1_ID\"
      }")

    if ! check_response "$POST_RESPONSE" "create post $i for Alice's Tech Blog"; then
      echo -e "${YELLOW}Continuing with other posts...${NC}"
    else
      echo -e "${GREEN}Created post $i for Alice's Tech Blog${NC}"
    fi
  done
fi

# Create post for Feed 2
echo -e "${YELLOW}Creating post for Alice's Travel Journal...${NC}"

# Skip post creation if feed ID is unknown
if [[ "$FEED2_ID" == "unknown" ]] || [[ "$USER1_ID" == "unknown" ]]; then
  echo -e "${RED}Skipping post creation for Alice's Travel Journal due to missing IDs${NC}"
else
  POST_RESPONSE=$(curl -s -X POST http://localhost:8000/feed/$FEED2_ID/post \
    -H "Content-Type: application/json" \
    -d "{
      \"content\": \"My first travel post: Visited Paris last weekend!\",
      \"user_id\": \"$USER1_ID\"
    }")

  if ! check_response "$POST_RESPONSE" "create post for Alice's Travel Journal"; then
    echo -e "${YELLOW}Continuing with other operations...${NC}"
  else
    echo -e "${GREEN}Created post for Alice's Travel Journal${NC}"
  fi
fi

# Create feed follows
echo -e "${YELLOW}Creating feed follows...${NC}"

# Alice follows Bob's feed
if [[ "$USER1_ID" != "unknown" && "$FEED4_ID" != "unknown" ]]; then
  echo -e "${YELLOW}Making Alice follow Bob's feed...${NC}"
  FOLLOW1_RESPONSE=$(curl -s -X POST http://localhost:8000/feed/$FEED4_ID/follow \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $(curl -s -X POST http://localhost:8000/auth/token \
      -H "Content-Type: application/x-www-form-urlencoded" \
      -d "username=<EMAIL>&password=password123" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)")

  if ! check_response "$FOLLOW1_RESPONSE" "make Alice follow Bob's feed"; then
    echo -e "${RED}Failed to make Alice follow Bob's feed${NC}"
  else
    echo -e "${GREEN}Alice is now following Bob's feed${NC}"
  fi
fi

# Alice follows Bob's Tech Blog (which has posts)
if [[ "$USER1_ID" != "unknown" && "$FEED1_ID" != "unknown" ]]; then
  echo -e "${YELLOW}Making Alice follow her own Tech Blog (for testing)...${NC}"
  FOLLOW5_RESPONSE=$(curl -s -X POST http://localhost:8000/feed/$FEED1_ID/follow \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $(curl -s -X POST http://localhost:8000/auth/token \
      -H "Content-Type: application/x-www-form-urlencoded" \
      -d "username=<EMAIL>&password=password123" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)")

  if ! check_response "$FOLLOW5_RESPONSE" "make Alice follow her own Tech Blog"; then
    echo -e "${RED}Failed to make Alice follow her own Tech Blog${NC}"
  else
    echo -e "${GREEN}Alice is now following her own Tech Blog (for testing)${NC}"
  fi
fi

# Alice follows her Travel Journal (which has a post)
if [[ "$USER1_ID" != "unknown" && "$FEED2_ID" != "unknown" ]]; then
  echo -e "${YELLOW}Making Alice follow her own Travel Journal (for testing)...${NC}"
  FOLLOW6_RESPONSE=$(curl -s -X POST http://localhost:8000/feed/$FEED2_ID/follow \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $(curl -s -X POST http://localhost:8000/auth/token \
      -H "Content-Type: application/x-www-form-urlencoded" \
      -d "username=<EMAIL>&password=password123" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)")

  if ! check_response "$FOLLOW6_RESPONSE" "make Alice follow her own Travel Journal"; then
    echo -e "${RED}Failed to make Alice follow her own Travel Journal${NC}"
  else
    echo -e "${GREEN}Alice is now following her own Travel Journal (for testing)${NC}"
  fi
fi

# Bob follows Alice's Tech Blog
if [[ "$USER2_ID" != "unknown" && "$FEED1_ID" != "unknown" ]]; then
  echo -e "${YELLOW}Making Bob follow Alice's Tech Blog...${NC}"
  FOLLOW2_RESPONSE=$(curl -s -X POST http://localhost:8000/feed/$FEED1_ID/follow \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $(curl -s -X POST http://localhost:8000/auth/token \
      -H "Content-Type: application/x-www-form-urlencoded" \
      -d "username=<EMAIL>&password=password123" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)")

  if ! check_response "$FOLLOW2_RESPONSE" "make Bob follow Alice's Tech Blog"; then
    echo -e "${RED}Failed to make Bob follow Alice's Tech Blog${NC}"
  else
    echo -e "${GREEN}Bob is now following Alice's Tech Blog${NC}"
  fi
fi

# Bob follows Alice's Travel Journal
if [[ "$USER2_ID" != "unknown" && "$FEED2_ID" != "unknown" ]]; then
  echo -e "${YELLOW}Making Bob follow Alice's Travel Journal...${NC}"
  FOLLOW3_RESPONSE=$(curl -s -X POST http://localhost:8000/feed/$FEED2_ID/follow \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $(curl -s -X POST http://localhost:8000/auth/token \
      -H "Content-Type: application/x-www-form-urlencoded" \
      -d "username=<EMAIL>&password=password123" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)")

  if ! check_response "$FOLLOW3_RESPONSE" "make Bob follow Alice's Travel Journal"; then
    echo -e "${RED}Failed to make Bob follow Alice's Travel Journal${NC}"
  else
    echo -e "${GREEN}Bob is now following Alice's Travel Journal${NC}"
  fi
fi

# Charlie follows Alice's Tech Blog
if [[ "$USER3_ID" != "unknown" && "$FEED1_ID" != "unknown" ]]; then
  echo -e "${YELLOW}Making Charlie follow Alice's Tech Blog...${NC}"
  FOLLOW4_RESPONSE=$(curl -s -X POST http://localhost:8000/feed/$FEED1_ID/follow \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $(curl -s -X POST http://localhost:8000/auth/token \
      -H "Content-Type: application/x-www-form-urlencoded" \
      -d "username=<EMAIL>&password=password123" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)")

  if ! check_response "$FOLLOW4_RESPONSE" "make Charlie follow Alice's Tech Blog"; then
    echo -e "${RED}Failed to make Charlie follow Alice's Tech Blog${NC}"
  else
    echo -e "${GREEN}Charlie is now following Alice's Tech Blog${NC}"
  fi
fi

# Create reposts
echo -e "${YELLOW}Creating reposts...${NC}"

# Bob reposts a post from Alice's Tech Blog to his Empty Feed
if [[ "$USER2_ID" != "unknown" && "$FEED1_ID" != "unknown" && "$FEED4_ID" != "unknown" ]]; then
  echo -e "${YELLOW}Bob reposts a post from Alice's Tech Blog to his Empty Feed...${NC}"

  # First, get a post ID from Alice's Tech Blog
  ALICE_TOKEN=$(curl -s -X POST http://localhost:8000/auth/token \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "username=<EMAIL>&password=password123" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)

  POST_ID=$(curl -s http://localhost:8000/feed/$FEED1_ID \
    -H "Authorization: Bearer $ALICE_TOKEN" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)

  if [[ -z "$POST_ID" ]]; then
    echo -e "${RED}Error: Could not get a post ID from Alice's Tech Blog${NC}"
  else
    echo -e "${GREEN}Found post ID: $POST_ID${NC}"

    # Create the repost
    REPOST_RESPONSE=$(curl -s -X POST http://localhost:8000/repost/ \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer $(curl -s -X POST http://localhost:8000/auth/token \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "username=<EMAIL>&password=password123" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)" \
      -d "{
        \"original_post_id\": \"$POST_ID\",
        \"target_feed_id\": \"$FEED4_ID\"
      }")

    if ! check_response "$REPOST_RESPONSE" "create repost"; then
      echo -e "${RED}Failed to create repost${NC}"
    else
      echo -e "${GREEN}Created repost successfully${NC}"
    fi
  fi
fi

# Charlie reposts a post from Alice's Travel Journal to his feed
if [[ "$USER3_ID" != "unknown" && "$FEED2_ID" != "unknown" ]]; then
  echo -e "${YELLOW}Charlie reposts a post from Alice's Travel Journal...${NC}"

  # First, get a post ID from Alice's Travel Journal
  POST_ID=$(curl -s http://localhost:8000/feed/$FEED2_ID  \
    -H "Authorization: Bearer $ALICE_TOKEN" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)

  if [[ -z "$POST_ID" ]]; then
    echo -e "${RED}Error: Could not get a post ID from Alice's Travel Journal${NC}"
  else
    echo -e "${GREEN}Found post ID: $POST_ID${NC}"

    # Create the repost
    REPOST_RESPONSE=$(curl -s -X POST http://localhost:8000/repost/ \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer $(curl -s -X POST http://localhost:8000/auth/token \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "username=<EMAIL>&password=password123" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)" \
      -d "{
        \"original_post_id\": \"$POST_ID\",
        \"target_feed_id\": \"$FEED4_ID\"
      }")

    if ! check_response "$REPOST_RESPONSE" "create repost"; then
      echo -e "${RED}Failed to create repost${NC}"
    else
      echo -e "${GREEN}Created repost successfully${NC}"
    fi
  fi
fi

# Print summary with status indicators
echo -e "\n${GREEN}Database population completed!${NC}"
echo -e "${GREEN}Summary:${NC}"

# User status
if [[ "$USER1_ID" == "unknown" ]]; then
  echo -e "  - ${RED}✗ alice (failed)${NC}"
else
  echo -e "  - ${GREEN}✓ alice${NC}"

  # Feed status for alice
  if [[ "$FEED1_ID" != "unknown" ]]; then
    echo -e "    ${GREEN}✓ Alice's Tech Blog (with posts)${NC}"
  else
    echo -e "    ${RED}✗ Alice's Tech Blog (failed)${NC}"
  fi

  if [[ "$FEED2_ID" != "unknown" ]]; then
    echo -e "    ${GREEN}✓ Alice's Travel Journal (with 1 post)${NC}"
  else
    echo -e "    ${RED}✗ Alice's Travel Journal (failed)${NC}"
  fi

  if [[ "$FEED3_ID" != "unknown" ]]; then
    echo -e "    ${GREEN}✓ Alice's Empty Feed (no posts)${NC}"
  else
    echo -e "    ${RED}✗ Alice's Empty Feed (failed)${NC}"
  fi

  # Alice's follows
  echo -e "    ${YELLOW}Follows:${NC}"
  if [[ "$FEED4_ID" != "unknown" ]]; then
    echo -e "      ${GREEN}✓ Bob's Empty Feed${NC}"
  fi
  if [[ "$FEED1_ID" != "unknown" ]]; then
    echo -e "      ${GREEN}✓ Alice's Tech Blog (with posts)${NC}"
  fi
  if [[ "$FEED2_ID" != "unknown" ]]; then
    echo -e "      ${GREEN}✓ Alice's Travel Journal (with 1 post)${NC}"
  fi
fi

# Bob status
if [[ "$USER2_ID" == "unknown" ]]; then
  echo -e "  - ${RED}✗ bob (failed)${NC}"
else
  echo -e "  - ${GREEN}✓ bob${NC}"

  # Feed status for bob
  if [[ "$FEED4_ID" != "unknown" ]]; then
    echo -e "    ${GREEN}✓ Bob's Empty Feed (no posts)${NC}"
  else
    echo -e "    ${RED}✗ Bob's Empty Feed (failed)${NC}"
  fi

  # Bob's follows
  echo -e "    ${YELLOW}Follows:${NC}"
  if [[ "$FEED1_ID" != "unknown" ]]; then
    echo -e "      ${GREEN}✓ Alice's Tech Blog${NC}"
  fi
  if [[ "$FEED2_ID" != "unknown" ]]; then
    echo -e "      ${GREEN}✓ Alice's Travel Journal${NC}"
  fi
fi

# Charlie status
if [[ "$USER3_ID" == "unknown" ]]; then
  echo -e "  - ${RED}✗ charlie (failed)${NC}"
else
  echo -e "  - ${GREEN}✓ charlie (no feeds)${NC}"

  # Charlie's follows
  echo -e "    ${YELLOW}Follows:${NC}"
  if [[ "$FEED1_ID" != "unknown" ]]; then
    echo -e "      ${GREEN}✓ Alice's Tech Blog${NC}"
  fi
fi
