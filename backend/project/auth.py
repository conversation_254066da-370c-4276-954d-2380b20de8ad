from datetime import datetime, timedelta, timezone
from typing import Optional
from jose import JW<PERSON><PERSON><PERSON>, jwt
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import OAuth2<PERSON>asswordBearer
from sqlalchemy.orm import Session
from database import get_db
from crud.user import get_user_by_email_or_username
from utils.hashes import verify_password
from models import UserModel
import os
from dotenv import load_dotenv
load_dotenv()

# Get secret key from environment or use a default for development
SECRET_KEY = os.environ.get("AUTH_SECRET_KEY", "09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24 * 7  # 7 days

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/auth/token", auto_error=False)

def get_token_from_request(request: Request, token: Optional[str] = Depends(oauth2_scheme)) -> Optional[str]:
    """
    Get token from either Authorization header or HTTP-only cookie.
    Prioritizes Authorization header for API clients, falls back to cookie for browser requests.
    """
    # First try to get token from Authorization header (for API clients)
    if token:
        return token

    # Fall back to cookie (for browser requests)
    return request.cookies.get("access_token")

def authenticate_user(db: Session, identifier: str, password: str) -> Optional[UserModel]:
    user = get_user_by_email_or_username(db, identifier)
    if not user:
        return None

    if not verify_password(password, user.hashed_password):
        return None

    return user

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def get_current_user(request: Request, db: Session = Depends(get_db)):
    token = get_token_from_request(request)

    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    if not token:
        raise credentials_exception

    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception

    from uuid import UUID
    user = get_user(db, UUID(user_id))
    if user is None:
        raise credentials_exception
    return user

# Import here to avoid circular imports
from crud.user import get_user

def get_optional_current_user(request: Request, db: Session = Depends(get_db)):
    """
    Similar to get_current_user but returns None instead of raising an exception if authentication fails.
    This is useful for endpoints that can work with or without authentication.
    """
    token = get_token_from_request(request)

    if not token:
        return None

    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: str = payload.get("sub")
        if user_id is None:
            return None

        from uuid import UUID
        user = get_user(db, UUID(user_id))
        return user
    except JWTError:
        return None
