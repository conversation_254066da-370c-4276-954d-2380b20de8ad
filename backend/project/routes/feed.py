from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from database import get_db
from schemas import Feed, FeedUpdate, Post, PostCreate, FeedCreate, FeedResponse, BasePostResponse
from crud.feed import get_feed, update_feed, delete_feed, create_feed
from crud.post import create_post, delete_post, get_post
from crud.user import get_user
from utils.format_responses import format_feed_response, get_posts
from utils.url_metadata import fetch_url_metadata
from auth import get_current_user
from models import UserModel

router = APIRouter()

##########################
# Manage the feed itself #
##########################
@router.post("/", response_model=Feed)
def create_feed_route(feed: FeedCreate, db: Session = Depends(get_db)):
    """Create a new feed."""
    if not get_user(db, feed.user_id):
        raise HTTPException(status_code=404, detail="User not found")
    return create_feed(db, feed)

@router.get("/{feed_id}", response_model=FeedResponse)
def get_feed_route(feed_id: UUID, db: Session = Depends(get_db)):
    """Get a single feed by its ID."""
    feed = get_feed(db, feed_id)
    if not feed:
        raise HTTPException(status_code=404, detail="Feed not found")
    response = format_feed_response(feed, db)
    return response

@router.put("/{feed_id}", response_model=Feed)
def update_feed_route(feed_id: UUID, feed: FeedUpdate, db: Session = Depends(get_db)):
    """Update a feed's information."""
    db_feed = get_feed(db, feed_id)
    if not db_feed:
        raise HTTPException(status_code=404, detail="Feed not found")
    return update_feed(db, feed_id, feed)

@router.delete("/{feed_id}")
def delete_feed_route(feed_id: UUID, db: Session = Depends(get_db)):
    """Delete a feed."""
    db_feed = get_feed(db, feed_id)
    if not db_feed:
        raise HTTPException(status_code=404, detail="Feed not found")

    # Delete the feed
    delete_feed(db, feed_id)

    # Return a simple success message
    return {"status": "success", "message": "Feed deleted successfully"}


###########################
# Manage posts themselves #
###########################
@router.post("/{feed_id}/post", response_model=Post)
def create_feed_post_route(feed_id: UUID, post: PostCreate, db: Session = Depends(get_db)):
    """Create a new post in a specific feed."""
    feed = get_feed(db, feed_id)
    if not feed:
        raise HTTPException(status_code=404, detail="Feed not found")

    if not get_user(db, post.user_id):
        raise HTTPException(status_code=404, detail="User not found")

    # Create a copy of the post with all fields
    sanitized_post = PostCreate(
        content=post.content,
        user_id=post.user_id,
        embed_type=post.embed_type,
        embed_url=post.embed_url,
        embed_post_id=post.embed_post_id
    )

    # If it's a link embed, fetch metadata
    if sanitized_post.embed_type == "link" and sanitized_post.embed_url:
        try:
            metadata = fetch_url_metadata(sanitized_post.embed_url)
            # Add metadata to the post
            sanitized_post_dict = sanitized_post.model_dump()
            sanitized_post_dict["embed_title"] = metadata["title"]
            sanitized_post_dict["embed_description"] = metadata["description"]
            sanitized_post_dict["embed_image"] = metadata["image"]
            return create_post(db, PostCreate(**sanitized_post_dict), feed_id)
        except Exception as e:
            print(f"Error fetching metadata: {e}")
            # Continue with post creation without metadata

    # If it's a post embed, verify the post exists
    if sanitized_post.embed_type == "post" and sanitized_post.embed_post_id:
        embedded_post = get_post(db, sanitized_post.embed_post_id)
        if not embedded_post:
            raise HTTPException(status_code=404, detail="Embedded post not found")

    return create_post(db, sanitized_post, feed_id)

@router.get("/{feed_id}/post/{post_id}", response_model=Post)
def get_feed_post_route(feed_id: UUID, post_id: int, db: Session = Depends(get_db)):
    """Get a specific post from a specific feed."""
    feed = get_feed(db, feed_id)
    if not feed:
        raise HTTPException(status_code=404, detail="Feed not found")

    post = get_post(db, post_id)
    if not post or post.feed_id != feed_id:
        raise HTTPException(status_code=404, detail="Post not found in this feed")

    return post

@router.delete("/{feed_id}/post/{post_id}")
def delete_feed_post_route(feed_id: UUID, post_id: int, db: Session = Depends(get_db)):
    """Delete a specific post from a specific feed."""
    feed = get_feed(db, feed_id)
    if not feed:
        raise HTTPException(status_code=404, detail="Feed not found")

    post = get_post(db, post_id)
    if not post or post.feed_id != feed_id:
        raise HTTPException(status_code=404, detail="Post not found in this feed")

    delete_post(db, post_id)

    # Return a simple success message
    return {"status": "success", "message": "Post deleted successfully"}

@router.get("/{feed_id}/posts/", response_model=List[BasePostResponse])
def get_feed_posts_route(
    feed_id: UUID,
    amount: Optional[int] = None,
    last_post_id: Optional[UUID] = None,
    current_user: UserModel = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get posts for a specific feed, with optional pagination."""
    feed = get_feed(db, feed_id)
    if not feed:
        raise HTTPException(status_code=404, detail="Feed not found")

    # Use the existing get_posts function to fetch posts
    posts = get_posts(db, feed_id, amount or 1, last_post_id, current_user.id)

    return posts
