from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from schemas import Post
from crud.post import get_post, delete_post
from database import get_db
from uuid import UUID
from utils.format_responses import format_post_response
from auth import get_current_user
from models import UserModel

router = APIRouter()

@router.get("/{post_id}", response_model=Post)
def get_post_route(
    post_id: str,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """Get a single post by its ID."""
    try:
        post_uuid = UUID(post_id)
        post = get_post(db, post_uuid)
        if not post:
            raise HTTPException(status_code=404, detail="Post not found")

        return format_post_response(post, db, current_user_id=current_user.id)
    except ValueError:
        raise HTTPException(status_code=422, detail="Invalid post ID format")

@router.delete("/{post_id}", response_model=Post)
def delete_post_route(post_id: str, db: Session = Depends(get_db)):
    """Delete a post by its ID."""
    try:
        post_uuid = UUID(post_id)
        post = get_post(db, post_uuid)
        if not post:
            raise HTTPException(status_code=404, detail="Post not found")
        return delete_post(db, post_uuid)
    except ValueError:
        raise HTTPException(status_code=422, detail="Invalid post ID format")
