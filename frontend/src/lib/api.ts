// No longer need auth imports - using HTTP-only cookies

// Base URL for API requests
export const API_BASE_URL = "http://localhost:8000";

type request_options = {
  method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  body?: Record<string, any> | null;
  headers?: Record<string, string>;
};

/**
 * Helper function for making API requests
 * @param endpoint - API endpoint (without base URL)
 * @param options - Request options
 * @returns Promise with the response
 */
export async function apiRequest<T = any>(
  endpoint: string,
  options: request_options = {}
): Promise<T> {
  const { method = "GET", body = null, headers = {} } = options;

  // Build the full URL
  const url = `${API_BASE_URL}${
    endpoint.startsWith("/") ? endpoint : `/${endpoint}`
  }`;

  // Prepare headers
  const request_headers: Record<string, string> = {
    "Content-Type": "application/json",
    ...headers,
  };

  // Prepare request options
  const request_options: RequestInit = {
    method,
    headers: request_headers,
    credentials: "include", // This ensures cookies are sent with requests
  };

  // Add body if provided
  if (body) {
    request_options.body = JSON.stringify(body);
  }

  // Make the request
  const response = await fetch(url, request_options);

  // Handle response
  // If the response is 204 No Content (common for DELETE operations), return an empty object
  if (response.status === 204) {
    return {} as T;
  }

  const content_type = response.headers.get("content-type");
  let response_body: any = null;

  if (content_type?.includes("application/json")) {
    try {
      response_body = await response.json();
    } catch (err) {
      console.error("Error parsing JSON response:", err);
      throw new Error(`Invalid JSON response from ${url}`);
    }
  }

  if (!response.ok) {
    const error_detail =
      response_body?.detail || "No detailed error message available";
    console.error("API request failed:", {
      url,
      status: response.status,
      statusText: response.statusText,
      body: error_detail,
    });
    throw new Error(
      `API request for ${url} failed with status ${response.status}: ${response.statusText} -- ${error_detail}`
    );
  }

  return response_body as T;
}
