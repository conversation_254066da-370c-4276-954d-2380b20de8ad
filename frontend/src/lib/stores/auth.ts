import { writable } from "svelte/store";
import type { User } from "$lib/types";
import { apiRequest } from "../api";

type AuthStore = {
  user: User | null;
  is_authenticated: boolean;
};

const initialState: AuthStore = {
  user: null,
  is_authenticated: false,
};

const createAuthStore = () => {
  const { subscribe, set, update } = writable<AuthStore>(initialState);

  return {
    subscribe,

    // Login: set the user data (token is handled by HTTP-only cookie)
    login: (userData: User) => {
      const authData = {
        user: userData,
        is_authenticated: true,
      };

      set(authData);
    },

    // Logout: clear the user data and call logout endpoint
    logout: async () => {
      try {
        // Call the logout endpoint to clear the HTTP-only cookie
        await apiRequest("/auth/logout", { method: "POST" });
      } catch (error) {
        console.error("Error during logout:", error);
      } finally {
        // Always reset the auth state
        set(initialState);
      }
    },

    // Check if the user is authenticated by making a request to a protected endpoint
    checkAuth: async (): Promise<boolean> => {
      try {
        // Try to fetch user info - this will work if the cookie is valid
        const response = await apiRequest<{ user: User }>("/auth/me");
        set({
          user: response.user,
          is_authenticated: true,
        });
        return true;
      } catch (error) {
        // If the request fails, user is not authenticated
        set(initialState);
        return false;
      }
    },
  };
};

export const auth = createAuthStore();
