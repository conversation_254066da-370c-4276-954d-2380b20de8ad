import { writable } from "svelte/store";
import type { User } from "$lib/types";

type AuthStore = {
  token: string | null;
  user: User | null;
  is_authenticated: boolean;
};

const initialState: AuthStore = {
  token: null,
  user: null,
  is_authenticated: false,
};

const createAuthStore = () => {
  // Try to load auth data from localStorage on initialization
  let initial_data = initialState;

  if (typeof window !== "undefined") {
    const storedAuth = localStorage.getItem("auth");
    if (storedAuth) {
      try {
        initial_data = JSON.parse(storedAuth);
      } catch (e) {
        console.error("Failed to parse stored auth data", e);
      }
    }
  }

  const { subscribe, set, update } = writable<AuthStore>(initial_data);

  return {
    subscribe,

    // Login: set the token and user data
    login: (token: string, userData: User) => {
      const authData = {
        token,
        user: userData,
        is_authenticated: true,
      };

      // Save to localStorage
      if (typeof window !== "undefined") {
        localStorage.setItem("auth", JSON.stringify(authData));
      }

      set(authData);
    },

    // Logout: clear the token and user data
    logout: () => {
      // Remove from localStorage
      if (typeof window !== "undefined") {
        localStorage.removeItem("auth");
      }

      set(initialState);
    },

    // Check if the token is still valid
    checkAuth: async () => {
      // This would typically validate the token with the server
      // For now, we'll just check if we have a token
      return update((state) => {
        if (!state.token) {
          return initialState;
        }
        return state;
      });
    },
  };
};

export const auth = createAuthStore();
