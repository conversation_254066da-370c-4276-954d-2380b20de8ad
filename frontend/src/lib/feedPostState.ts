import { writable, get, type Writable } from "svelte/store";
import type { Post } from "./types";
import { apiRequest } from "./api";
import { auth } from "./stores/auth";

export type FeedPostState = {
  current_posts: Post[];
  current_index: number;
  total_posts_count: number;
};

const createFeedPostStatesStore = () => {
  const feed_post_states: Writable<Record<string, FeedPostState>> = writable(
    {}
  );

  const initializeFeed = (
    feed_id: string,
    initial_posts: Post[],
    total_posts_count: number
  ) => {
    feed_post_states.update((states) => {
      const newStates = { ...states };
      newStates[feed_id] = {
        current_posts: initial_posts,
        current_index: 0,
        total_posts_count,
      };
      return newStates;
    });
  };

  const viewOlderPost = async (feed_id: string) => {
    const states = get(feed_post_states);
    const feedData = { ...states[feed_id] };
    if (!feedData) {
      return;
    }

    // If there are no more total posts, return
    if (!hasOlderPost(feed_id)) {
      return;
    }

    feedData.current_index++;

    // Load more posts if we need to
    if (feedData.current_index >= feedData.current_posts.length - 1) {
      console.log(feedData.current_posts[feedData.current_index]);
      const newPosts = await apiRequest<Post[]>(
        `feed/${feed_id}/posts/?amount=1&last_post_id=${
          feedData.current_posts[feedData.current_index].id
        }`,
        {
          token: get(auth).token,
        }
      );
      feedData.current_posts = [...feedData.current_posts, ...newPosts];
    }

    // Update the store
    feed_post_states.update((states) => ({
      ...states,
      [feed_id]: feedData,
    }));
  };

  const viewNewerPost = async (feed_id: string) => {
    const states = get(feed_post_states);
    const feedData = { ...states[feed_id] };
    if (!feedData) {
      return;
    }

    if (!hasNewerPost(feed_id)) {
      return;
    }

    feedData.current_index--;

    feed_post_states.update((states) => ({
      ...states,
      [feed_id]: feedData,
    }));
  };

  const getCurrentPost = (feed_id: string): Post | null => {
    const states = get(feed_post_states);
    const feedData = { ...states[feed_id] };

    if (!feedData) {
      return null;
    }

    if (feedData.current_posts.length === 0) {
      return null;
    }

    if (feedData.current_index >= feedData.current_posts.length) {
      return null;
    }

    return feedData.current_posts[feedData.current_index];
  };

  // Helper function to check if there are previous (older) posts
  const hasOlderPost = (feed_id: string): boolean => {
    const states = get(feed_post_states);
    const feedData = { ...states[feed_id] };

    if (!feedData) {
      return false;
    }

    console.log(feedData.current_index, feedData.total_posts_count - 1);
    return feedData.current_index < feedData.total_posts_count - 1;
  };

  // Check if there are newer posts
  const hasNewerPost = (feed_id: string): boolean => {
    const states = get(feed_post_states);
    const feedData = states[feed_id];

    if (!feedData) {
      return false;
    }

    return feedData.current_index > 0;
  };

  return {
    subscribe: feed_post_states.subscribe,
    initializeFeed,
    viewOlderPost,
    viewNewerPost,
    getCurrentPost,
    hasOlderPost,
    hasNewerPost,
  };
};

// Create a singleton instance of the store
export const feed_post_states = createFeedPostStatesStore();
