<script lang="ts">
  import { goto } from "$app/navigation";
  import { auth } from "$lib/stores/auth";
  import Button from "$lib/components/Button.svelte";

  function handleLogout() {
    auth.logout();
    goto("/about");
  }
</script>

<nav class="navbar">
  <div class="navbar-brand">
    <a href="/">noalgo</a>
  </div>

  <div class="navbar-menu">
    {#if $auth.is_authenticated}
      <a href="/" class="navbar-item">Timeline</a>
      <a href="/feeds" class="navbar-item">Feeds</a>
      <a href={`/profile/${$auth.user?.username}`} class="navbar-item">
        Profile
      </a>
      <Button href="/create">Create</Button>
      <Button onclick={handleLogout} appearance="tertiary">Logout</Button>
    {:else}
      <Button href="/login">Login</Button>
      <Button href="/signup">Sign up</Button>
    {/if}
  </div>
</nav>

<style>
  .navbar {
    position: sticky;
    top: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .navbar-brand a {
    font-size: 1.5rem;
    font-weight: 700;
    color: #4a90e2;
    text-decoration: none;
  }

  .navbar-menu {
    display: flex;
    align-items: center;
    gap: 1.5rem;
  }

  .navbar-item {
    color: #555;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s;
  }

  .navbar-item:hover {
    color: #4a90e2;
  }
</style>
