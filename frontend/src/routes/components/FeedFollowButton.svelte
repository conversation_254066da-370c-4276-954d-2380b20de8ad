<script lang="ts">
  import { auth } from "$lib/stores/auth";
  import Button from "$lib/components/Button.svelte";
  import { apiRequest } from "$lib/api";

  const { feed_id } = $props<{ feed_id: string }>();

  let is_following = $state(false);
  let is_loading = $state(false);
  let follow_id = $state<string | null>(null);

  // Reactive statement to check follow status when auth state changes
  $effect(() => {
    if ($auth.is_authenticated && $auth.token) {
      checkFollowStatus();
    }
  });

  async function follow() {
    try {
      const data = await apiRequest<{ id: string }>(`feed/${feed_id}/follow`, {
        method: "POST",
      });

      is_following = true;
      follow_id = data.id;
    } catch (error) {
      console.error("Error following feed:", error);
    }
  }

  async function unfollow() {
    try {
      await apiRequest(`feed/${feed_id}/unfollow`, {
        method: "DELETE",
      });

      is_following = false;
      follow_id = null;
    } catch (error) {
      console.error("Error unfollowing feed:", error);
    }
  }

  async function checkFollowStatus() {
    // Only make the API call if user is authenticated and has a valid token
    if (!$auth.is_authenticated || !$auth.token) {
      is_following = false;
      follow_id = null;
      return;
    }

    try {
      is_loading = true;
      const data = await apiRequest<{
        is_following: boolean;
        follow_id: string | null;
      }>(`feed/${feed_id}/follow-status`);

      is_following = data.is_following;
      follow_id = data.follow_id;
    } catch (error) {
      console.error("Error checking follow status:", error);
      // Set default values on error
      is_following = false;
      follow_id = null;
    } finally {
      is_loading = false;
    }
  }

  async function toggleFollow() {
    if (!$auth.is_authenticated) {
      window.location.href = "/login";
      return;
    }

    try {
      is_loading = true;

      if (is_following) {
        await unfollow();
      } else {
        await follow();
      }
    } catch (error) {
      console.error("Error toggling follow status:", error);
    } finally {
      is_loading = false;
    }
  }
</script>

<Button onclick={toggleFollow} disabled={is_loading}>
  {#if is_loading}
    <span class="loading-spinner"></span>
  {:else if is_following}
    Unfollow
  {:else}
    Follow
  {/if}
</Button>

<style>
  .loading-spinner {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
</style>
