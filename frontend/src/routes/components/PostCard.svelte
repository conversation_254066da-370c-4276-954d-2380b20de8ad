<script lang="ts">
  import { auth } from "$lib/stores/auth";
  import type { Post } from "$lib/types";
  import ConfirmModal from "$lib/components/ConfirmModal.svelte";
  import Button from "$lib/components/Button.svelte";
  import { apiRequest } from "$lib/api";
  import Repost from "./Repost.svelte";
  import LikeButton from "./LikeButton.svelte";
  import LinkEmbed from "./LinkEmbed.svelte";
  import PostEmbed from "./PostEmbed.svelte";
  import { goto } from "$app/navigation";
  import { sanitizeContent } from "$lib/utils/sanitize";

  const {
    post,
    show_feed_info = true,
    on_delete,
  } = $props<{
    post: Post;
    show_feed_info?: boolean;
    on_delete?: (postId: string) => void;
  }>();

  const is_current_users_posts = $derived(
    $auth.user && post.user_id === $auth.user.id
  );

  let error = $state("");

  // Sanitize the post content with line breaks preserved
  const sanitizedContent = $derived(sanitizeContent(post.content, true));

  const popover_id = `delete-post-${post.id}`;

  function closeModal() {
    (document.getElementById(popover_id) as HTMLDialogElement)?.hidePopover();
  }

  async function deletePost(postId: string) {
    try {
      error = "";

      apiRequest(`feed/${post.feed_id}/post/${postId}`, {
        method: "DELETE",
        token: $auth.token, // Explicitly pass the token
      });

      on_delete?.(postId);
    } catch (err) {
      console.error("Error deleting post:", err);
      error = err instanceof Error ? err.message : "Error deleting post";
    }
  }

  function handleConfirm() {
    deletePost(post.id);
    closeModal();
  }

  function handleCancel() {
    closeModal();
  }

  function quoteRepost() {
    goto(`/create?quote=${post.id}`);
  }
</script>

<div class="post-card {post.is_repost ? 'repost' : ''}">
  {#if error}
    <div class="error-message">
      <p>{error}</p>
    </div>
  {/if}

  {#if post.is_repost}
    <div class="repost-header">
      <span class="repost-icon">↻</span>
      <span class="repost-text">
        Reposted by <a href="/profile/{post.reposted_by_username}">
          {post.reposted_by_username}
        </a>
        from
        <a href="/feed/{post.original_feed_id}">{post.original_feed_name}</a>
      </span>
    </div>
  {/if}

  <div class="post-header">
    <div class="post-info">
      <p class="post-author">
        Posted by: <a href="/profile/{post.username}">{post.username}</a>
      </p>
      {#if show_feed_info && post.feed_name}
        <p class="post-feed">
          in <a href="/feed/{post.feed_id}">{post.feed_name}</a>
        </p>
      {/if}
    </div>
    <p class="post-date">
      Posted: {new Date(post.created_at).toLocaleString()}
      {#if post.is_repost && post.repost_created_at}
        <br />
        Reposted: {new Date(post.repost_created_at).toLocaleString()}
      {/if}
    </p>
  </div>

  <div class="post-content">
    <p>{@html sanitizedContent}</p>

    {#if post.embed_type === "link" && post.embed_url}
      <LinkEmbed
        url={post.embed_url}
        title={post.embed_title}
        description={post.embed_description}
        image={post.embed_image}
      />
    {/if}

    {#if post.embed_type === "post" && post.embedded_post}
      <PostEmbed post={post.embedded_post} />
    {/if}
  </div>

  <div class="post-footer">
    <div class="post-actions">
      <LikeButton
        postId={post.id}
        initialLikeCount={post.like_count}
        initialIsLiked={post.is_liked}
      />

      {#if $auth.is_authenticated}
        <Repost postId={post.id} />

        <Button appearance="secondary" onclick={quoteRepost}>Quote</Button>
      {/if}

      {#if is_current_users_posts}
        <div class="delete-container">
          <Button popovertarget={popover_id}>Delete post</Button>

          <ConfirmModal
            id={popover_id}
            title="Delete post"
            message="Are you sure you want to delete this post? This action cannot be undone."
            confirm_text="Delete"
            on_confirm={handleConfirm}
            on_cancel={handleCancel}
          />
        </div>
      {/if}
    </div>
  </div>
</div>

<style>
  .post-card {
    background-color: #fff;
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
  }

  .post-card.repost {
    border-left: 4px solid #2ecc71;
  }

  .repost-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 14px;
    color: #2ecc71;
  }

  .repost-icon {
    font-size: 16px;
    margin-right: 5px;
  }

  .repost-text a {
    color: #2ecc71;
    text-decoration: none;
    font-weight: 500;
  }

  .repost-text a:hover {
    text-decoration: underline;
  }

  .post-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 14px;
    color: #777;
  }

  .post-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .post-author a,
  .post-feed a {
    color: #4a90e2;
    text-decoration: none;
    font-weight: 500;
  }

  .post-author a:hover,
  .post-feed a:hover {
    text-decoration: underline;
  }

  .post-content {
    margin-bottom: 15px;
  }

  .post-footer {
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid #eee;
    padding-top: 10px;
    margin-top: 10px;
  }

  .post-actions {
    display: flex;
    gap: 10px;
  }

  .error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 20px;
  }
</style>
