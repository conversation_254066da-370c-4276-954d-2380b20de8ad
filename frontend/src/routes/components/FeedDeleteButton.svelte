<script lang="ts">
  import { auth } from "$lib/stores/auth";
  import { goto } from "$app/navigation";
  import { apiRequest } from "$lib/api";
  import Button from "$lib/components/Button.svelte";
  import ConfirmModal from "$lib/components/ConfirmModal.svelte";

  const { feed_id } = $props<{
    feed_id: string;
  }>();

  const popover_id = `delete-feed-${feed_id}`;

  let is_deleting = $state(false);
  let error = $state("");

  async function handleConfirm() {
    try {
      is_deleting = true;
      error = "";

      apiRequest(`feed/${feed_id}`, {
        method: "DELETE",
        token: $auth.token, // Explicitly pass the token
      });

      // Go to the user's profile
      goto(`/profile/${$auth.user?.username}`);
    } catch (err) {
      console.error("Error deleting feed:", err);
      error = err instanceof Error ? err.message : "Error deleting feed";
      is_deleting = false;
    }
  }

  function closeModal() {
    (document.getElementById(popover_id) as HTMLDialogElement)?.hidePopover();
  }

  function handleCancel() {
    closeModal();
  }
</script>

<div class="feed-delete-container">
  {#if error}
    <div class="error-message">{error}</div>
  {/if}

  <Button
    appearance="tertiary"
    popovertarget={popover_id}
    disabled={is_deleting}
  >
    {is_deleting ? "Deleting..." : "Delete feed"}
  </Button>

  <ConfirmModal
    id={popover_id}
    title="Delete feed"
    message="Are you sure you want to delete this feed? This action cannot be undone."
    confirm_text="Delete"
    on_confirm={handleConfirm}
    on_cancel={handleCancel}
  />
</div>

<style>
  .feed-delete-container {
    margin-top: 10px;
  }

  .error-message {
    color: #e74c3c;
    margin-bottom: 10px;
    font-size: 14px;
  }
</style>
