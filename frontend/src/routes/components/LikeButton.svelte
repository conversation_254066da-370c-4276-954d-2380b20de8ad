<script lang="ts">
  import { auth } from "$lib/stores/auth";
  import { apiRequest } from "$lib/api";
  import Button from "$lib/components/Button.svelte";

  const {
    postId,
    initialLikeCount = 0,
    initialIsLiked = false,
  } = $props<{
    postId: string;
    initialLikeCount?: number;
    initialIsLiked?: boolean;
  }>();

  // State for the button
  let like_count = $state(initialLikeCount);
  let is_liked = $state(initialIsLiked);
  let is_loading = $state(false);
  let error = $state("");

  async function toggleLike() {
    if (!$auth.is_authenticated) {
      error = "You must be logged in to like posts";
      setTimeout(() => {
        error = "";
      }, 3000);
      return;
    }

    try {
      is_loading = true;
      error = "";

      if (is_liked) {
        // Unlike the post
        await apiRequest(`like/${postId}`, {
          method: "DELETE",
          token: $auth.token, // Explicitly pass the token
        });
        like_count--;
        is_liked = false;
      } else {
        // Like the post
        await apiRequest(`like/`, {
          method: "POST",
          token: $auth.token, // Explicitly pass the token
          body: {
            user_id: $auth.user?.id,
            post_id: postId,
          },
        });
        like_count++;
        is_liked = true;
      }
    } catch (err) {
      console.error("Error toggling like:", err);
      error = err instanceof Error ? err.message : "Error toggling like";
    } finally {
      is_loading = false;
    }
  }
</script>

<div class="like-container">
  {#if error}
    <div class="error-message">{error}</div>
  {:else}
    <Button
      appearance={is_liked ? "primary" : "secondary"}
      onclick={toggleLike}
      disabled={is_loading}
    >
      {is_liked ? "Liked" : "Like"}
      {like_count > 0 ? `(${like_count})` : ""}
    </Button>
  {/if}
</div>

<style>
  .like-container {
    display: inline-block;
  }

  .error-message {
    color: #e74c3c;
    font-size: 14px;
    padding: 6px 12px;
    border-radius: 4px;
    background-color: rgba(231, 76, 60, 0.1);
    border: 1px solid rgba(231, 76, 60, 0.3);
  }
</style>
