<script lang="ts">
  import { auth } from "$lib/stores/auth";
  import { apiRequest } from "$lib/api";
  import type { Feed } from "$lib/types";
  import Button from "$lib/components/Button.svelte";
  import BaseModal from "$lib/components/Base/BaseModal.svelte";

  const { postId } = $props<{
    postId: number;
  }>();

  // State for the button
  let show_success_message = $state(false);

  // State for the modal
  let feeds = $state<Feed[]>([]);
  let selected_feed_id = $state("");
  let is_submitting = $state(false);
  let error = $state("");
  let is_loading = $state(true);

  const popover_id = `repost-popover-${postId}`;

  function closeModal() {
    (document.getElementById(popover_id) as HTMLDialogElement)?.hidePopover();
  }

  // Function to fetch user's feeds
  async function fetchUserFeeds() {
    if (!$auth.user?.id) {
      error = "User not authenticated";
      is_loading = false;
      return;
    }

    try {
      is_loading = true;
      error = "";

      // Fetch user's feeds
      const user_feeds = await apiRequest<Feed[]>(
        `feeds/?user_id=${$auth.user.id}`,
        {
          token: $auth.token, // Explicitly pass the token
        }
      );

      if (user_feeds && Array.isArray(user_feeds)) {
        feeds = user_feeds;
      } else {
        console.error("Invalid feeds response:", user_feeds);
        feeds = [];
        error = "Failed to load feeds";
      }
    } catch (err) {
      console.error("Error fetching feeds:", err);
      error = err instanceof Error ? err.message : "Error fetching feeds";
      feeds = [];
    } finally {
      is_loading = false;
    }
  }

  // Function to handle modal open
  function onModalOpen() {
    fetchUserFeeds();
  }

  // Form submission handler
  async function handleSubmit() {
    if (!selected_feed_id) {
      error = "Please select a feed";
      return;
    }

    try {
      is_submitting = true;
      error = "";

      // Create the repost
      await apiRequest(`repost/`, {
        method: "POST",
        token: $auth.token, // Explicitly pass the token
        body: {
          user_id: $auth.user?.id,
          feed_id: selected_feed_id,
          original_post_id: postId,
        },
      });

      // Close the modal and show success message
      closeModal();
      show_success_message = true;

      // Hide success message after 3 seconds
      setTimeout(() => {
        show_success_message = false;
      }, 3000);
    } catch (err) {
      console.error("Error reposting:", err);
      error = err instanceof Error ? err.message : "Error reposting";
    } finally {
      is_submitting = false;
    }
  }

  function handleCancel() {
    closeModal();
  }
</script>

<div class="repost-container">
  {#if show_success_message}
    <div class="success-message">Post reposted successfully!</div>
  {:else}
    <Button popovertarget={popover_id} onclick={onModalOpen}>Repost</Button>
  {/if}

  <BaseModal id={popover_id} title="Repost to your feed">
    {#if is_loading}
      <div class="loading">Loading your feeds...</div>
    {:else if feeds.length === 0}
      <div class="no-feeds">
        <p>You don't have any feeds to repost to.</p>
        <p>
          <a href="/create" class="create-feed-link">Create a feed</a> first.
        </p>
      </div>
    {:else}
      <form
        onsubmit={(e) => {
          e.preventDefault();
          handleSubmit();
        }}
      >
        <div class="form-group">
          <label for={`feed-select-${postId}`}>
            Select a feed to repost to:
          </label>
          <select
            id={`feed-select-${postId}`}
            bind:value={selected_feed_id}
            required
          >
            <option value="" disabled selected>Choose a feed...</option>
            {#each feeds as feed}
              <option value={feed.id}>{feed.name}</option>
            {/each}
          </select>
        </div>

        {#if error}
          <div class="error-message">{error}</div>
        {/if}

        <div class="modal-actions">
          <Button onclick={handleCancel} disabled={is_submitting}>Cancel</Button
          >
          <Button type="submit" disabled={is_submitting || !selected_feed_id}>
            {is_submitting ? "Reposting..." : "Repost"}
          </Button>
        </div>
      </form>
    {/if}
  </BaseModal>
</div>

<style>
  /* Button styles */
  .repost-container {
    display: inline-block;
  }

  .success-message {
    color: #2ecc71;
    font-size: 14px;
    font-weight: 500;
    padding: 6px 12px;
    border-radius: 4px;
    background-color: rgba(46, 204, 113, 0.1);
    border: 1px solid rgba(46, 204, 113, 0.3);
  }

  /* Form styles */
  .form-group {
    margin-bottom: 20px;
  }

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
  }

  select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background-color: white;
  }

  .modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
  }

  .error-message {
    color: #e74c3c;
    margin-top: 10px;
    font-size: 14px;
  }

  .loading,
  .no-feeds {
    padding: 20px 0;
    color: #555;
  }

  .create-feed-link {
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
  }

  .create-feed-link:hover {
    text-decoration: underline;
  }
</style>
