import type { PageServerLoad } from "./$types";
import type { Post } from "$lib/types";
import { error } from "@sveltejs/kit";
import { apiRequest } from "$lib/api";

export const load: PageServerLoad = async ({ locals }) => {
  try {
    // Get the auth token from the session
    const token = locals.token;

    // If there's no token, return empty data
    if (!token) {
      return {
        posts: [],
        is_authenticated: false,
      };
    }

    try {
      // Fetch posts from followed feeds
      const posts = await apiRequest<Post[]>("feed-posts/followed", {
        token,
      });

      return {
        posts,
        is_authenticated: true,
      };
    } catch (err) {
      // If unauthorized, return empty data
      if (err instanceof Error && err.message.includes("401")) {
        return {
          posts: [],
          is_authenticated: false,
        };
      }

      // Re-throw other errors
      throw err;
    }
  } catch (err) {
    console.error("Error loading feed posts:", err);
    throw error(500, "Error loading feed posts");
  }
};
