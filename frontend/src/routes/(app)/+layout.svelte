<script lang="ts">
  import { onMount } from "svelte";
  import { auth } from "$lib/stores/auth";
  import { goto } from "$app/navigation";
  import HeaderContent from "../components/HeaderContent.svelte";
  import LeftSidebarContent from "../components/LeftSidebarContent.svelte";
  import RightSidebarContent from "../components/RightSidebarContent.svelte";
  import Navbar from "../components/Navbar.svelte";

  const { children } = $props();

  onMount(() => {
    // Check authentication on mount
    auth.checkAuth();

    // Redirect to about page if not authenticated
    if (!$auth.is_authenticated) {
      goto("/about");
    }
  });
</script>

<Navbar />

<div class="page-layout">
  <header>
    <HeaderContent />
  </header>
  <aside class="left-sidebar">
    <LeftSidebarContent />
  </aside>
  <main>
    {@render children()}
  </main>
  <aside class="right-sidebar">
    <RightSidebarContent />
  </aside>
</div>

<style>
  .page-layout {
    display: grid;
    grid-template-areas:
      "header header header"
      "left-sidebar main right-sidebar";
    grid-template-columns: 1fr 3fr 1fr;
    grid-template-rows: auto 1fr;
    height: 100vh;
  }

  header {
    grid-area: header;
  }

  .left-sidebar {
    grid-area: left-sidebar;
  }

  main {
    grid-area: main;
  }

  .right-sidebar {
    grid-area: right-sidebar;
  }
</style>
