<script lang="ts">
  import { goto } from "$app/navigation";
  import { auth } from "$lib/stores/auth";
  import type { LoginCredentials, AuthResponse } from "$lib/types";
  import { API_BASE_URL } from "$lib/api";
  import Button from "$lib/components/Button.svelte";

  let credentials: LoginCredentials = {
    identifier: "",
    password: "",
  };

  let error = $state("");
  let loading = $state(false);

  async function handleLogin() {
    error = "";
    loading = true;

    try {
      // Use form data format for OAuth2 token endpoint
      const form_data = new URLSearchParams();
      form_data.append("username", credentials.identifier); // OAuth2 expects 'username' field
      form_data.append("password", credentials.password);

      // Note: We're using direct fetch here because OAuth2 requires form-urlencoded format
      // which is different from our standard API helper
      const response = await fetch(`${API_BASE_URL}/auth/token`, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        credentials: "include", // Include cookies for HTTP-only cookie auth
        body: form_data,
      });

      if (!response.ok) {
        let errorMessage = "Login failed";
        try {
          const errorData = await response.json();
          errorMessage = errorData.detail || errorMessage;
          console.error("Login error response:", errorData);
        } catch (e) {
          // If we can't parse the error response, just use the default message
          console.error("Error parsing login error:", e);
        }
        throw new Error(errorMessage);
      }

      const data: AuthResponse = await response.json();

      // Store the user data (token is now handled by HTTP-only cookie)
      auth.login({
        id: data.user_id,
        username: data.username,
        email: credentials.identifier.includes("@")
          ? credentials.identifier
          : "", // Only set email if it looks like an email
        status: "active",
        created_at: new Date().toISOString(),
      });

      // Redirect to home page
      goto("/");
    } catch (err) {
      if (err instanceof Error) {
        error = err.message;
      } else {
        error = "An unknown error occurred";
      }
    } finally {
      loading = false;
    }
  }
</script>

<div class="login-container">
  <div class="login-card">
    <h1>Login</h1>

    {#if error}
      <div class="error-message">
        {error}
      </div>
    {/if}

    <form
      onsubmit={(e) => {
        e.preventDefault();
        handleLogin();
      }}
    >
      <div class="form-group">
        <label for="identifier">Email or Username</label>
        <input
          type="text"
          id="identifier"
          bind:value={credentials.identifier}
          required
          placeholder="Enter your email or username"
          disabled={loading}
        />
      </div>

      <div class="form-group">
        <label for="password">Password</label>
        <input
          type="password"
          id="password"
          bind:value={credentials.password}
          required
          disabled={loading}
        />
      </div>

      <Button type="submit" disabled={loading}>
        {loading ? "Logging in..." : "Login"}
      </Button>
    </form>

    <div class="signup-link">
      Don't have an account? <a href="/signup">Sign up</a>
    </div>
  </div>
</div>

<style>
  .login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 80vh;
    padding: 20px;
  }

  .login-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 30px;
    width: 100%;
    max-width: 400px;
  }

  h1 {
    margin-top: 0;
    margin-bottom: 24px;
    color: #333;
    text-align: center;
  }

  .form-group {
    margin-bottom: 20px;
  }

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
  }

  input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
  }

  input:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
  }

  .error-message {
    background-color: #ffebee;
    color: #d32f2f;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 20px;
    font-size: 14px;
  }

  .signup-link {
    margin-top: 20px;
    text-align: center;
    font-size: 14px;
    color: #666;
  }

  .signup-link a {
    color: #4a90e2;
    text-decoration: none;
  }

  .signup-link a:hover {
    text-decoration: underline;
  }
</style>
