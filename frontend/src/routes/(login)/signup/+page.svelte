<script lang="ts">
  import { goto } from "$app/navigation";
  import { auth } from "$lib/stores/auth";
  import type { AuthResponse } from "$lib/types";
  import { apiRequest, API_BASE_URL } from "$lib/api";
  import <PERSON>ton from "$lib/components/Button.svelte";

  let form_data = {
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
  };

  let error = $state("");
  let loading = $state(false);

  async function handleSignup() {
    error = "";
    loading = true;

    // Validate passwords match
    if (form_data.password !== form_data.confirmPassword) {
      error = "Passwords do not match";
      loading = false;
      return;
    }

    try {
      await apiRequest("users/", {
        method: "POST",
        body: {
          username: form_data.username,
          email: form_data.email,
          password: form_data.password,
        },
      });

      // Now automatically log in the user
      // We can use either email or username as the identifier
      await loginAfterSignup(form_data.email, form_data.password);

      // Redirect to home page
      goto("/");
    } catch (err) {
      if (err instanceof Error) {
        error = err.message;
      } else {
        error = "An unknown error occurred";
      }
    } finally {
      loading = false;
    }
  }

  // Function to log in the user after successful signup
  async function loginAfterSignup(identifier: string, password: string) {
    try {
      // Use form data format for OAuth2 token endpoint
      const form_data = new URLSearchParams();
      form_data.append("username", identifier);
      form_data.append("password", password);

      // Note: We're using direct fetch here because OAuth2 requires form-urlencoded format
      // which is different from our standard API helper
      const response = await fetch(`${API_BASE_URL}/auth/token`, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: form_data,
      });

      if (!response.ok) {
        console.error("Auto-login failed after signup");
        return false;
      }

      const data: AuthResponse = await response.json();

      // Store the token and user data
      auth.login(data.access_token, {
        id: data.user_id,
        username: data.username,
        email: identifier.includes("@") ? identifier : "", // Only set email if it looks like an email
        status: "active",
        created_at: new Date().toISOString(),
      });

      return true;
    } catch (error) {
      console.error("Error during auto-login:", error);
      return false;
    }
  }
</script>

<div class="signup-container">
  <div class="signup-card">
    <h1>Create account</h1>

    {#if error}
      <div class="error-message">
        {error}
      </div>
    {/if}

    <form
      onsubmit={(e) => {
        e.preventDefault();
        handleSignup();
      }}
    >
      <div class="form-group">
        <label for="username">Username</label>
        <input
          type="text"
          id="username"
          bind:value={form_data.username}
          required
          disabled={loading}
        />
      </div>

      <div class="form-group">
        <label for="email">Email</label>
        <input
          type="email"
          id="email"
          bind:value={form_data.email}
          required
          disabled={loading}
        />
      </div>

      <div class="form-group">
        <label for="password">Password</label>
        <input
          type="password"
          id="password"
          bind:value={form_data.password}
          required
          disabled={loading}
        />
      </div>

      <div class="form-group">
        <label for="confirmPassword">Confirm Password</label>
        <input
          type="password"
          id="confirmPassword"
          bind:value={form_data.confirmPassword}
          required
          disabled={loading}
        />
      </div>

      <Button type="submit" disabled={loading}>
        {loading ? "Creating account..." : "Sign up"}
      </Button>
    </form>

    <div class="login-link">
      Already have an account? <a href="/login">Log in</a>
    </div>
  </div>
</div>

<style>
  .signup-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 80vh;
    padding: 20px;
  }

  .signup-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 30px;
    width: 100%;
    max-width: 400px;
  }

  h1 {
    margin-top: 0;
    margin-bottom: 24px;
    color: #333;
    text-align: center;
  }

  .form-group {
    margin-bottom: 20px;
  }

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
  }

  input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
  }

  input:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
  }

  .error-message {
    background-color: #ffebee;
    color: #d32f2f;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 20px;
    font-size: 14px;
  }

  .login-link {
    margin-top: 20px;
    text-align: center;
    font-size: 14px;
    color: #666;
  }

  .login-link a {
    color: #4a90e2;
    text-decoration: none;
  }

  .login-link a:hover {
    text-decoration: underline;
  }
</style>
