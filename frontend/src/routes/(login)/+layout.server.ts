import type { LayoutServerLoad } from "./$types";
import { redirect } from "@sveltejs/kit";

export const load: LayoutServerLoad = async ({ locals }) => {
  // Check if user is already authenticated
  if (locals.token) {
    // If already logged in, redirect to homepage
    throw redirect(302, "/");
  }

  // If not logged in, allow access to login/signup pages
  return {
    // No data needed for this layout
  };
};
