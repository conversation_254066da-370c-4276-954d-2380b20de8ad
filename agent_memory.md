# noalgo project memory file

This file contains the project preferences and guidelines for the noalgo project. When working with this project, please read this file first and follow these guidelines.

**IMPORTANT**: When starting a new conversation about this project, reference this file and ask the AI to use these guidelines instead of any previous memory.

## Set up

- There's a .env file in the root directory with the necessary environment variables. Don't create new .env files.
- When working with this repo, start by running the backend for the app and executing populate_db.sh to set up the database. Make sure it runs successfully. If it doesn't, stop what you're working on and ask if you should fix the issue before continuing. Then run the front-end also.

## Front-end

- Use Svelte 5 (not previous Svelte versions) for the front-end with regular, modern CSS.
- Use Svelte 5 runes mode with $props(), $derived, and other Svelte 5 features instead of traditional reactivity.
- Use reactive variables with $state() in Svelte 5 components.
- Svelte 5 provides alternatives for the deprecated page import as documented at svelte.dev/docs/kit/migrating-to-sveltekit-2#SvelteKit-2.12:-$app-stores-deprecated.
- User notes that createEventDispatcher is deprecated in Svelte 5, and wants parent components to handle modal confirmation/rejection logic.
- This project's front-end uses TypeScript so please add types whenever you're adding or modifying TypeScript.
- You can import from $lib when importing in the front-end.
- Use TypeScript types instead of interfaces when defining data structures.
- User prefers to move shared types and methods to a common location for reuse across components.
- Use sentence case, not title case, for all headings and button labels.
- User wants consistent UI components across feed displays and requires confirmation dialogs when users delete their posts or feeds.
- User prefers to combine tightly coupled components like RepostButton and RepostModal into a single file.
- User prefers using Svelte's bind:this directive for accessing DOM elements within the same page instead of using getElementById.
- When implementing UI components that might appear multiple times on the same page (like repost buttons), ensure all IDs used are unique to prevent conflicts.
- Functions on the frontend should use camelCase. Variables on the frontend should use snake_case. Types of the front-end should use PascalCase.
- User prefers automatic URL-to-embed conversion while typing with an option to remove embeds.
- User-generated content should be sanitized on the front-end to prevent XSS vulnerabilities.
- User prefers to use shared components when appropriate in the front-end for consistency and maintainability.

## Database

- User prefers to use UUID version 7 for database identifiers.
- When creating database scripts, only include data population functionality without data deletion or docker commands.
- Backend variables should use snake_case (e.g., post_count).

## API design

- User prefers deletion endpoints to return a success indicator rather than the deleted entity itself.
- User prefers to fetch all essential post-related data in a single server-side request, while non-essential data like follows, likes, or comments can be requested separately.

## Code style

- Don't add explanatory comments to code that are just for the chat; only use code comments for things future engineers need to know when looking at the code.
- User prefers to use shared constants for values used across the application, such as the amount of posts to load in different views.
- User prefers creating helper methods for API calls with a configurable base URL constant to make code more maintainable and production-ready.
- User prefers to avoid redundant fields in the codebase if they store the same value.

## Display and functionality

- Reposts should be shown according to their repost time (not original post time) and should appear in FeedCard views with proper counting.
- When retrieving posts from feeds, always include reposts; don't use separate methods for posts with and without reposts.
- When displaying post counts, show the total post count rather than the current post length.
- When displaying reposts, show the time at which a post was reposted on the front-end, and use sort_time for ordering when retrieving posts with last_post_id.
- User wants to remove markdown support and instead add functionality for embedding links (showing meta image, title, description) and posts (rendered like quote retweets).
- User wants quote reposts to create posts with embedded posts.
- Posts and quoted reposts should have individual like functionality, while non-quote reposts should share like functionality with their original posts.

## Backend

- User prefers to run the backend without Docker.
- User prefers to run the backend in a virtual environment with the command: cd /Users/<USER>/Downloads/GitHub/noalgo/backend && python3 -m venv venv && source venv/bin/activate && pip install -r requirements.txt
- User prefers flexible data models that can handle optional fields rather than adding fields to match rigid schemas.

## How to use this memory file

1. When starting a new conversation about this project, share this file with the AI.
2. Ask the AI to read and follow these guidelines instead of using its built-in memory.
3. Example prompt: "I'm working on the noalgo project. Please read the augment_memory.md file and use these guidelines instead of any previous memory you might have about this project."

This approach ensures consistent assistance across different sessions and with different AI assistants.
